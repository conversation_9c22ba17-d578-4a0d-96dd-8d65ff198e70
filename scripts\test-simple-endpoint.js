// Test simple Vercel endpoint
async function testSimpleEndpoint() {
  const baseUrl = 'https://inno-crm-admin.vercel.app'
  
  console.log('🔍 Testing Simple Vercel Endpoint...\n')
  
  try {
    console.log('Testing /api/test endpoint...')
    const response = await fetch(`${baseUrl}/api/test`)
    
    if (response.ok) {
      const data = await response.json()
      console.log('✅ Simple endpoint works!')
      console.log(JSON.stringify(data, null, 2))
    } else {
      console.log(`❌ Simple endpoint failed: ${response.status}`)
      const errorText = await response.text()
      console.log('Error:', errorText)
    }
    
  } catch (error) {
    console.error('❌ Error testing simple endpoint:', error.message)
  }
}

// Run the test
testSimpleEndpoint()
