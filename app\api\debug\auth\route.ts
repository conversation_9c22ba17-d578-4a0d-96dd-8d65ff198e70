import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

export async function POST(request: NextRequest) {
  try {
    const { phone, password } = await request.json()
    
    if (!phone || !password) {
      return NextResponse.json({
        error: 'Phone and password required',
        timestamp: new Date().toISOString()
      }, { status: 400 })
    }

    const prisma = new PrismaClient()
    
    // Find user
    const user = await prisma.user.findUnique({
      where: { phone },
      select: {
        id: true,
        phone: true,
        name: true,
        email: true,
        role: true,
        password: true,
        createdAt: true
      }
    })

    if (!user) {
      await prisma.$disconnect()
      return NextResponse.json({
        success: false,
        error: 'User not found',
        phone,
        timestamp: new Date().toISOString()
      })
    }

    // Check password
    const passwordMatch = await bcrypt.compare(password, user.password)
    
    await prisma.$disconnect()

    return NextResponse.json({
      success: passwordMatch,
      user: {
        id: user.id,
        phone: user.phone,
        name: user.name,
        email: user.email,
        role: user.role,
        created: user.createdAt
      },
      passwordMatch,
      serverType: process.env.SERVER_TYPE || 'unknown',
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}
