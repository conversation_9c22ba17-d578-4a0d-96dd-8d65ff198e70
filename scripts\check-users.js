require('dotenv').config({ path: '.env.local' })
const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function checkUsers() {
  try {
    console.log('🔍 Checking users in database...\n')
    
    const users = await prisma.user.findMany({
      select: {
        id: true,
        phone: true,
        name: true,
        email: true,
        role: true,
        createdAt: true,
      },
      orderBy: { createdAt: 'asc' }
    })

    if (users.length === 0) {
      console.log('❌ No users found in database')
      return
    }

    console.log(`✅ Found ${users.length} users:`)
    console.log('=' .repeat(80))
    
    users.forEach((user, index) => {
      console.log(`${index + 1}. ${user.name}`)
      console.log(`   Phone: ${user.phone}`)
      console.log(`   Email: ${user.email || 'N/A'}`)
      console.log(`   Role: ${user.role}`)
      console.log(`   Created: ${user.createdAt.toISOString()}`)
      console.log('')
    })

    // Check if the working credentials exist
    const workingUser = await prisma.user.findUnique({
      where: { phone: '+998906006299' },
      select: {
        id: true,
        phone: true,
        name: true,
        role: true,
        createdAt: true,
      }
    })

    if (workingUser) {
      console.log('🎯 Working credentials found:')
      console.log(`   User: ${workingUser.name}`)
      console.log(`   Phone: ${workingUser.phone}`)
      console.log(`   Role: ${workingUser.role}`)
      console.log(`   This user exists in your local database!`)
    } else {
      console.log('❌ Working credentials (+998906006299) not found in database')
    }

  } catch (error) {
    console.error('❌ Error checking users:', error.message)
  } finally {
    await prisma.$disconnect()
  }
}

checkUsers()
