import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'

export async function GET(request: NextRequest) {
  try {
    // Check environment variables
    const envCheck = {
      DATABASE_URL: process.env.DATABASE_URL ? 'SET' : 'MISSING',
      NEXTAUTH_SECRET: process.env.NEXTAUTH_SECRET ? 'SET' : 'MISSING',
      NEXTAUTH_URL: process.env.NEXTAUTH_URL || 'MISSING',
      SERVER_TYPE: process.env.SERVER_TYPE || 'MISSING',
      INTER_SERVER_SECRET: process.env.INTER_SERVER_SECRET ? 'SET' : 'MISSING',
    }

    // Test database connection
    let dbStatus = 'UNKNOWN'
    let userCount = 0
    let testUser = null

    try {
      const prisma = new PrismaClient()
      
      // Test basic connection
      await prisma.$connect()
      dbStatus = 'CONNECTED'
      
      // Count users
      userCount = await prisma.user.count()
      
      // Check for the specific test user
      testUser = await prisma.user.findUnique({
        where: { phone: '+998906006299' },
        select: {
          id: true,
          phone: true,
          name: true,
          role: true,
          createdAt: true
        }
      })
      
      await prisma.$disconnect()
    } catch (dbError) {
      dbStatus = `ERROR: ${dbError.message}`
    }

    return NextResponse.json({
      timestamp: new Date().toISOString(),
      environment: 'VERCEL',
      envVariables: envCheck,
      database: {
        status: dbStatus,
        userCount,
        testUserExists: !!testUser,
        testUser: testUser ? {
          name: testUser.name,
          phone: testUser.phone,
          role: testUser.role,
          created: testUser.createdAt
        } : null
      }
    })

  } catch (error) {
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}
